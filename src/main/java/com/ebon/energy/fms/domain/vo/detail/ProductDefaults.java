package com.ebon.energy.fms.domain.vo.detail;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 产品默认配置
 * 包含产品的默认设置和支持的功能
 */
@Data
public class ProductDefaults {

    @JsonProperty("SupportsConnectedPV")
    private boolean supportsConnectedPV;

    @JsonProperty("DefaultBatteryCapacity")
    private double defaultBatteryCapacity;

    @JsonProperty("MaxPVCapacity")
    private double maxPVCapacity;

    @JsonProperty("DefaultGridConnection")
    private boolean defaultGridConnection;
}
