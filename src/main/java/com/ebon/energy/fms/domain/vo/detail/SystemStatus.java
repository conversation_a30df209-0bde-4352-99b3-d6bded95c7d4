package com.ebon.energy.fms.domain.vo.detail;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 系统状态类
 * 用于存储和处理系统状态信息
 */
@Data
@Slf4j
public class SystemStatus {

    @JsonProperty("Status")
    private String status;

    @JsonProperty("LastUpdated")
    private String lastUpdated;

    @JsonProperty("Details")
    private String details;

    /**
     * 从JSON字符串反序列化SystemStatus对象
     * @param json JSON字符串
     * @return SystemStatus对象，如果反序列化失败则返回null
     */
    public static SystemStatus deserialize(String json) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }

        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(json, SystemStatus.class);
        } catch (JsonProcessingException e) {
            log.error("Failed to deserialize SystemStatus from JSON: {}", json, e);
            return null;
        }
    }

    /**
     * 将SystemStatus对象序列化为JSON字符串
     * @return JSON字符串，如果序列化失败则返回null
     */
    public String serialize() {
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.writeValueAsString(this);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize SystemStatus to JSON", e);
            return null;
        }
    }
}
