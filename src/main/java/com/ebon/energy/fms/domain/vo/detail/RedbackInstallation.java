package com.ebon.energy.fms.domain.vo.detail;

import com.ebon.energy.fms.domain.vo.Address;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Redback安装信息
 * 包含完整的安装详情和产品信息
 */
@Data
public class RedbackInstallation {

    @JsonProperty("RedbackProductSn")
    private String redbackProductSn;

    @JsonProperty("SiteId")
    private String siteId;

    @JsonProperty("Address")
    private Address address;

    @JsonProperty("RedbackProductInstallationDetails")
    private List<RedbackProductInstallationDetail> redbackProductInstallationDetails;

    @JsonProperty("RedbackProduct")
    private RedbackProduct redbackProduct;

    @JsonProperty("InstallationStartDate")
    private LocalDateTime installationStartDate;
}
