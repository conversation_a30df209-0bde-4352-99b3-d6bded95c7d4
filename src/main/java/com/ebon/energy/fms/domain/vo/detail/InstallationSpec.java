package com.ebon.energy.fms.domain.vo.detail;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 安装规格
 * 包含安装相关的规格要求和限制
 */
@Data
public class InstallationSpec {

    @JsonProperty("MustHaveGrid")
    private boolean mustHaveGrid;

    @JsonProperty("RequiresNMI")
    private boolean requiresNMI;

    @JsonProperty("MaxSolarPanels")
    private int maxSolarPanels;

    @JsonProperty("SupportedDirections")
    private String[] supportedDirections;
}
