package com.ebon.energy.fms.domain.vo;

import com.ebon.energy.fms.common.utils.IRedbackAddress;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 地址实体类
 * 实现IRedbackAddress接口，用于地址信息的存储和处理
 */
@Data
public class Address implements IRedbackAddress {

    @JsonProperty("GooglePlaceId")
    private String googlePlaceId;

    @JsonProperty("AddressLineOne")
    private String addressLineOne;

    @JsonProperty("AddressLineTwo")
    private String addressLineTwo;

    @JsonProperty("Suburb")
    private String suburb;

    @JsonProperty("State")
    private String state;

    @JsonProperty("Country")
    private String country;

    @JsonProperty("PostCode")
    private String postCode;

    @JsonProperty("Latitude")
    private String latitude;

    @JsonProperty("Longitude")
    private String longitude;

    @JsonProperty("TimeZoneId")
    private String timeZoneId;

    /**
     * 格式化地址为完整地址字符串
     * @return 格式化的地址字符串
     */
    public String formattedAddress() {
        StringBuilder address = new StringBuilder();

        if (addressLineOne != null && !addressLineOne.trim().isEmpty()) {
            address.append(addressLineOne);
        }

        if (suburb != null && !suburb.trim().isEmpty()) {
            if (address.length() > 0) address.append(", ");
            address.append(suburb);
        }

        if (state != null && !state.trim().isEmpty()) {
            if (address.length() > 0) address.append(", ");
            address.append(state);
        }

        if (postCode != null && !postCode.trim().isEmpty()) {
            if (address.length() > 0) address.append(" ");
            address.append(postCode);
        }

        if (country != null && !country.trim().isEmpty()) {
            if (address.length() > 0) address.append(", ");
            address.append(country);
        }

        return address.toString();
    }
}
