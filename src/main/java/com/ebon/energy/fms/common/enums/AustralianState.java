package com.ebon.energy.fms.common.enums;

import lombok.Getter;

/**
 * 澳大利亚州枚举
 * 定义澳大利亚的各个州和领地
 */
@Getter
public enum AustralianState {
    NSW("NSW", "New South Wales", "新南威尔士州"),
    VIC("VIC", "Victoria", "维多利亚州"),
    QLD("QLD", "Queensland", "昆士兰州"),
    WA("WA", "Western Australia", "西澳大利亚州"),
    SA("SA", "South Australia", "南澳大利亚州"),
    TAS("TAS", "Tasmania", "塔斯马尼亚州"),
    ACT("ACT", "Australian Capital Territory", "澳大利亚首都领地"),
    NT("NT", "Northern Territory", "北领地");

    private final String code;
    private final String fullName;
    private final String chineseName;

    AustralianState(String code, String fullName, String chineseName) {
        this.code = code;
        this.fullName = fullName;
        this.chineseName = chineseName;
    }

    /**
     * 根据代码或全名查找州
     * @param value 州代码或全名
     * @return 对应的州枚举，如果未找到则返回null
     */
    public static AustralianState fromString(String value) {
        if (value == null || value.trim().isEmpty()) {
            return null;
        }

        String trimmedValue = value.trim();
        for (AustralianState state : values()) {
            if (state.code.equalsIgnoreCase(trimmedValue) ||
                state.fullName.equalsIgnoreCase(trimmedValue)) {
                return state;
            }
        }
        return null;
    }
}
