package com.ebon.energy.fms.common.utils;

import com.ebon.energy.fms.common.utils.google.GoogleAddress;
import com.ebon.energy.fms.common.utils.google.Results;
import com.ebon.energy.fms.domain.vo.AddressDTO;
import com.ebon.energy.fms.domain.vo.detail.GeoCodeResult;
import com.ebon.energy.fms.domain.vo.detail.IViewModelAddress;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;

import javax.validation.ValidationException;
import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;

@Data
public class GeoUtility implements IGeoUtility {

    private static final String USER_DEFINED = "UserDefined";
    private static final String EDIT_MANUALLY = "Please edit the address manually.";
    private static final String COULD_NOT_FIND = "Could not find this address.";
    private static final String COULD_NOT_FIND_TIMEZONE = "Could not find Timezone for this address.";

    private static final String MAPS_KEY =
            System.getProperty("GoogleMapsPlacesApiKey",
                    System.getenv("GoogleMapsPlacesApiKey"));
    private static final String MAPS_TIMEZONE_KEY =
            System.getProperty("GoogleMapsPlacesTimeZoneApiKey",
                    System.getenv("GoogleMapsPlacesTimeZoneApiKey"));

    private final HttpClient http = HttpClient.newHttpClient();
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public IRedbackAddress geoCodeAddress(IViewModelAddress address) throws ValidationException {
        if (address == null) return null;
        String placeId = address.getPlaceId();
        if (placeId == null || placeId.isEmpty() || !USER_DEFINED.equals(placeId
        )) {
            return null;
        }
        String full = address.getFullAddress();
        String suburb = address.getSuburb();
        if (full == null || suburb == null || !full.contains(suburb)) {
            throw new ValidationException(EDIT_MANUALLY);
        }
        String street = (address.getStreetNumber() == null ? "" : address.getStreetNumber())
                + " "
                + (address.getStreetName() == null ? "" : address.getStreetName(
        ));
        return mapRedbackAddress(street, suburb, address.getState(), address.getCountry(), address.getPostCode());
    }

    @Override
    public GeoCodeResult geoCodeAddress(String streetAddress, String suburb, String state, String country, String postcode)
            throws ValidationException {
        return mapAddress(streetAddress, suburb, state, country, postcode);
    }

    @Override
    public String timeZoneAddress(IRedbackAddress address) throws ValidationException {
        if (address == null) return "";
        return timeZoneAddress(address.getLatitude(), address.getLongitude());
    }

    @Override
    public String timeZoneAddress(String latitude, String longitude) throws ValidationException {
        try {
            // 保留与 C# 语义：通过 GoogleMaps Time Zone API 查询
            GoogleMapsTimeZoneApi timezoneapi = new GoogleMapsTimeZoneApi(MAPS_TIMEZONE_KEY);
            String timezone = timezoneapi.getTimeZone(latitude, longitude); //同步调用
            if (timezone == null || timezone.isEmpty()) {
                throw new ValidationException(COULD_NOT_FIND_TIMEZONE);
            }
            return timezone;
        } catch (ValidationException e) {
            throw e;
        } catch (Exception e) {
            throw new IllegalStateException(e.getMessage(), e);
        }
    }

    private IRedbackAddress mapRedbackAddress(String streetAddress, String suburb, String state, String country, String postcode)
            throws ValidationException {
        GeoCodeResult result = mapAddress(streetAddress, suburb, state, country,
                postcode);
        IRedbackAddress newAddress = new AddressDTO(); // 现有类型，未新建类
        newAddress.setGooglePlaceId(result.getPlaceId());
        newAddress.setLatitude(result.getLatitude());
        newAddress.setLongitude(result.getLongitude());
        return newAddress;
    }

    private GeoCodeResult mapAddress(String streetAddress, String suburb, String
            state, String country, String postcode)
            throws ValidationException {
        try {
            String url = String.format(
                    "https://maps.googleapis.com/maps/api/geocode/json?address=% s,+%s,+%s,+%s,+%s&key=%s",
                    encode(streetAddress), encode(suburb), encode(state), encode
                            (country), encode(postcode), MAPS_KEY);

            HttpRequest req = HttpRequest.newBuilder(URI.create(url)).GET().build();
            HttpResponse<String> resp = http.send(req, HttpResponse.BodyHandlers
                    .ofString());
            String body = resp.body();

            GoogleAddress googleAddress = objectMapper.readValue(body, GoogleAddress.class);

            if (googleAddress.getResults() == null || googleAddress.getResults()
                    .length == 0) {
                throw new ValidationException(COULD_NOT_FIND);
            }

            Results googleResults;
            if (googleAddress.getResults().length > 1) {
                googleResults = java.util.Arrays.stream(googleAddress.getResults())
                        .filter(x -> x.getFormattedAddress() != null && x.getFormattedAddress().contains(postcode))
                        .findFirst()
                        .orElse(null);
                if (googleResults == null) {
                    throw new ValidationException(COULD_NOT_FIND);
                }
            } else {
                googleResults = googleAddress.getResults()[0];
            }

            if (googleResults.getGeometry() == null || googleResults.getGeometry
                    ().getLocation() == null) {
                throw new ValidationException(COULD_NOT_FIND);
            }

            String lat = googleResults.getGeometry().getLocation().getLat();
            String lng = googleResults.getGeometry().getLocation().getLng();

            return new GeoCodeResult(googleResults.getPlaceId(), lat, lng);
        } catch (ValidationException e) {
            throw e;
        } catch (IOException | InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new IllegalStateException(e.getMessage(), e);
        } catch (Exception e) {
            throw new IllegalStateException(e.getMessage(), e);
        }
    }

    private static String encode(String s) {
        return s == null ? "" : java.net.URLEncoder.encode(s, java.nio.charset.StandardCharsets.UTF_8);
    }
}