package com.ebon.energy.fms.common.feature;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 门户刷新功能特性
 * 用于控制门户刷新功能的启用状态
 */
@Component
public class PortalRefreshFeature {

    @Value("${portal.refresh.enabled:false}")
    private boolean featureEnabled;

    /**
     * 检查功能是否启用
     * @return 如果功能启用返回true，否则返回false
     */
    public boolean isFeatureEnabled() {
        return featureEnabled;
    }

    /**
     * 设置功能启用状态
     * @param enabled 启用状态
     */
    public void setFeatureEnabled(boolean enabled) {
        this.featureEnabled = enabled;
    }
}
