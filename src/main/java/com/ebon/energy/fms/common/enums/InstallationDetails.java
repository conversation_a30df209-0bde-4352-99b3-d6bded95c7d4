package com.ebon.energy.fms.common.enums;

import lombok.Getter;

/**
 * 安装详情枚举
 * 定义安装过程中的各种详细信息类型
 */
@Getter
public enum InstallationDetails {
    SolarPanelManufacturer("SolarPanelManufacturer", "太阳能板制造商"),
    SolarPanelType("SolarPanelType", "太阳能板类型"),
    IsOffgrid("IsOffgrid", "是否离网"),
    ConnectionPointIdentifier("ConnectionPointIdentifier", "连接点标识符"),
    ConnectionPointIdentifierOptOut("ConnectionPointIdentifierOptOut", "连接点标识符退出"),
    NumberOfPanels1("NumberOfPanels1", "第一组太阳能板数量"),
    NumberOfPanels2("NumberOfPanels2", "第二组太阳能板数量"),
    NumberOfPanels3("NumberOfPanels3", "第三组太阳能板数量"),
    PVSize1("PVSize1", "第一组太阳能板功率"),
    PVSize2("PVSize2", "第二组太阳能板功率"),
    PVSize3("PVSize3", "第三组太阳能板功率"),
    PanelDirection1("PanelDirection1", "第一组太阳能板方向"),
    PanelDirection2("PanelDirection2", "第二组太阳能板方向"),
    PanelDirection3("PanelDirection3", "第三组太阳能板方向");

    private final String key;
    private final String description;

    InstallationDetails(String key, String description) {
        this.key = key;
        this.description = description;
    }
}
