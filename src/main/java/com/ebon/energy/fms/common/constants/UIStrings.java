package com.ebon.energy.fms.common.constants;

/**
 * UI字符串常量类
 * 定义用户界面中使用的各种字符串常量
 */
public class UIStrings {

    /**
     * 通知错误模型状态键
     */
    public static final String NOTIFY_ERROR_MODELSTATE_KEY = "error";

    /**
     * 成功消息键
     */
    public static final String SUCCESS_MESSAGE_KEY = "successMessage";

    /**
     * 错误消息键
     */
    public static final String ERROR_MESSAGE_KEY = "errorMessage";

    /**
     * 验证错误消息
     */
    public static final String VALIDATION_ERROR = "验证失败";

    /**
     * 操作成功消息
     */
    public static final String OPERATION_SUCCESS = "操作成功";

    /**
     * 操作失败消息
     */
    public static final String OPERATION_FAILED = "操作失败";

    /**
     * 私有构造函数，防止实例化
     */
    private UIStrings() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}
