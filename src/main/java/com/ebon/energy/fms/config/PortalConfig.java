package com.ebon.energy.fms.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "portal")
public class PortalConfig {

    private String url;

    private Integer maxDaysDateRange;

    private Boolean redbackBatterySwitch;

    private BigDecimal batteryFullThreshold;

    private String listOfStorageReductionJobs;

    private Integer exportLimitMessageThresholdW;

    private Integer numberOfConsecutiveDaysBatteriesMismatchedToDisabled;

    private Long siteChartMaxLookAheadAllowanceInSec;

}