package com.ebon.energy.fms.service;

import java.util.Map;

/**
 * 设置服务接口
 * 定义系统设置相关的操作方法
 */
public interface ISettingsService {

    /**
     * 获取设置值
     * @param key 设置键
     * @return 设置值，如果不存在则返回null
     */
    String getSetting(String key);

    /**
     * 获取设置值，如果不存在则返回默认值
     * @param key 设置键
     * @param defaultValue 默认值
     * @return 设置值或默认值
     */
    String getSetting(String key, String defaultValue);

    /**
     * 设置值
     * @param key 设置键
     * @param value 设置值
     */
    void setSetting(String key, String value);

    /**
     * 获取所有设置
     * @return 设置映射
     */
    Map<String, String> getAllSettings();

    /**
     * 批量设置
     * @param settings 设置映射
     */
    void setSettings(Map<String, String> settings);

    /**
     * 删除设置
     * @param key 设置键
     */
    void removeSetting(String key);
}
