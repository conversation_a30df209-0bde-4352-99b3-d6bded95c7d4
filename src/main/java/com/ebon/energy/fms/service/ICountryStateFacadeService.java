package com.ebon.energy.fms.service;

import com.ebon.energy.fms.domain.vo.detail.CountryStateDTO;

import java.util.List;

/**
 * 国家州门面服务接口
 * 定义国家和州相关的操作方法
 */
public interface ICountryStateFacadeService {

    /**
     * 获取所有州信息
     * @param countryCode 国家代码，如果为null则获取所有国家的州信息
     * @return 州信息列表
     */
    List<CountryStateDTO> getAllStates(String countryCode);

    /**
     * 根据国家获取州信息
     * @param countryCode 国家代码
     * @return 州信息列表
     */
    List<CountryStateDTO> getStatesByCountry(String countryCode);

    /**
     * 验证州是否属于指定国家
     * @param countryCode 国家代码
     * @param stateCode 州代码
     * @return 如果属于返回true，否则返回false
     */
    boolean isValidStateForCountry(String countryCode, String stateCode);
}
